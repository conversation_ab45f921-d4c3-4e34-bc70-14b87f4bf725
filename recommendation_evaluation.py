#!/usr/bin/env python3
"""
推荐系统评估方法详解和实践

这个脚本展示了如何全面评估推荐系统的性能，包括：
1. 预测准确性评估
2. 推荐质量评估  
3. 多样性和覆盖率分析
4. 业务指标评估
"""

import numpy as np
import pandas as pd
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

class RecommendationEvaluator:
    """推荐系统评估器"""
    
    def __init__(self):
        self.metrics = {}
    
    def evaluate_prediction_accuracy(self, y_true, y_pred):
        """
        评估预测准确性
        
        Args:
            y_true: 真实评分
            y_pred: 预测评分
        
        Returns:
            dict: 准确性指标
        """
        print("📊 预测准确性评估")
        print("-" * 40)
        
        # RMSE - 均方根误差
        rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))
        
        # MAE - 平均绝对误差  
        mae = np.mean(np.abs(y_true - y_pred))
        
        # MAPE - 平均绝对百分比误差
        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
        
        # R² - 决定系数
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        r2 = 1 - (ss_res / ss_tot)
        
        accuracy_metrics = {
            'RMSE': rmse,
            'MAE': mae, 
            'MAPE': mape,
            'R²': r2
        }
        
        print(f"RMSE (均方根误差): {rmse:.4f}")
        print(f"MAE (平均绝对误差): {mae:.4f}")
        print(f"MAPE (平均绝对百分比误差): {mape:.2f}%")
        print(f"R² (决定系数): {r2:.4f}")
        
        print("\n💡 指标解释:")
        print("• RMSE: 预测值与真实值的偏差程度，越小越好")
        print("• MAE: 预测误差的平均值，越小越好")
        print("• MAPE: 相对误差百分比，越小越好")
        print("• R²: 模型解释数据变异的程度，越接近1越好")
        
        return accuracy_metrics
    
    def evaluate_ranking_quality(self, user_recommendations, user_ground_truth, k_values=[5, 10, 20]):
        """
        评估排序质量
        
        Args:
            user_recommendations: {user_id: [recommended_items]}
            user_ground_truth: {user_id: [relevant_items]}
            k_values: 评估的K值列表
        
        Returns:
            dict: 排序质量指标
        """
        print("\n🎯 推荐质量评估")
        print("-" * 40)
        
        ranking_metrics = {}
        
        for k in k_values:
            precisions = []
            recalls = []
            f1_scores = []
            hit_rates = []
            ndcgs = []
            
            for user_id in user_recommendations:
                if user_id not in user_ground_truth:
                    continue
                
                recommended = set(user_recommendations[user_id][:k])
                relevant = set(user_ground_truth[user_id])
                
                if len(relevant) == 0:
                    continue
                
                # 计算交集
                intersection = recommended.intersection(relevant)
                
                # Precision@K
                precision = len(intersection) / len(recommended) if len(recommended) > 0 else 0
                precisions.append(precision)
                
                # Recall@K  
                recall = len(intersection) / len(relevant) if len(relevant) > 0 else 0
                recalls.append(recall)
                
                # F1@K
                f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                f1_scores.append(f1)
                
                # Hit Rate@K
                hit_rate = 1 if len(intersection) > 0 else 0
                hit_rates.append(hit_rate)
                
                # NDCG@K (简化版本)
                dcg = sum([1 / np.log2(i + 2) for i, item in enumerate(user_recommendations[user_id][:k]) if item in relevant])
                idcg = sum([1 / np.log2(i + 2) for i in range(min(k, len(relevant)))])
                ndcg = dcg / idcg if idcg > 0 else 0
                ndcgs.append(ndcg)
            
            # 计算平均值
            ranking_metrics[f'Precision@{k}'] = np.mean(precisions) if precisions else 0
            ranking_metrics[f'Recall@{k}'] = np.mean(recalls) if recalls else 0
            ranking_metrics[f'F1@{k}'] = np.mean(f1_scores) if f1_scores else 0
            ranking_metrics[f'Hit_Rate@{k}'] = np.mean(hit_rates) if hit_rates else 0
            ranking_metrics[f'NDCG@{k}'] = np.mean(ndcgs) if ndcgs else 0
            
            print(f"\nTop-{k} 推荐质量:")
            print(f"  Precision@{k}: {ranking_metrics[f'Precision@{k}']:.4f}")
            print(f"  Recall@{k}: {ranking_metrics[f'Recall@{k}']:.4f}")
            print(f"  F1@{k}: {ranking_metrics[f'F1@{k}']:.4f}")
            print(f"  Hit Rate@{k}: {ranking_metrics[f'Hit_Rate@{k}']:.4f}")
            print(f"  NDCG@{k}: {ranking_metrics[f'NDCG@{k}']:.4f}")
        
        print("\n💡 指标解释:")
        print("• Precision@K: 推荐列表中相关物品的比例")
        print("• Recall@K: 相关物品中被推荐的比例")
        print("• F1@K: Precision和Recall的调和平均")
        print("• Hit Rate@K: 至少命中一个相关物品的用户比例")
        print("• NDCG@K: 考虑排序位置的推荐质量指标")
        
        return ranking_metrics
    
    def evaluate_diversity(self, user_recommendations, item_features):
        """
        评估推荐多样性
        
        Args:
            user_recommendations: {user_id: [recommended_items]}
            item_features: {item_id: {'category': str, 'year': int, ...}}
        
        Returns:
            dict: 多样性指标
        """
        print("\n🌈 推荐多样性分析")
        print("-" * 40)
        
        all_recommended_items = []
        all_categories = []
        all_years = []
        
        for user_id, items in user_recommendations.items():
            all_recommended_items.extend(items)
            for item in items:
                if item in item_features:
                    all_categories.append(item_features[item].get('category', 'Unknown'))
                    all_years.append(item_features[item].get('year', 0))
        
        # 物品多样性 (去重率)
        unique_items = len(set(all_recommended_items))
        total_items = len(all_recommended_items)
        item_diversity = unique_items / total_items if total_items > 0 else 0
        
        # 类别多样性
        unique_categories = len(set(all_categories))
        category_diversity = unique_categories / len(set(item_features[item].get('category', 'Unknown') 
                                                       for item in item_features)) if item_features else 0
        
        # 年份分布
        year_range = max(all_years) - min(all_years) if all_years else 0
        
        diversity_metrics = {
            'Item_Diversity': item_diversity,
            'Category_Diversity': category_diversity,
            'Year_Range': year_range,
            'Unique_Items': unique_items,
            'Total_Recommendations': total_items
        }
        
        print(f"物品多样性: {item_diversity:.4f} ({unique_items}/{total_items})")
        print(f"类别覆盖率: {category_diversity:.4f}")
        print(f"年份跨度: {year_range} 年")
        
        print("\n💡 多样性重要性:")
        print("• 避免推荐结果过于单一和重复")
        print("• 帮助用户发现新的兴趣点")
        print("• 提升用户体验和满意度")
        
        return diversity_metrics
    
    def evaluate_coverage(self, user_recommendations, total_items):
        """
        评估推荐覆盖率
        
        Args:
            user_recommendations: {user_id: [recommended_items]}
            total_items: 总物品数量
        
        Returns:
            dict: 覆盖率指标
        """
        print("\n📈 推荐覆盖率分析")
        print("-" * 40)
        
        # 物品覆盖率
        all_recommended = set()
        for items in user_recommendations.values():
            all_recommended.update(items)
        
        item_coverage = len(all_recommended) / total_items if total_items > 0 else 0
        
        # 用户覆盖率 (假设所有用户都有推荐)
        user_coverage = len(user_recommendations) / len(user_recommendations) if user_recommendations else 0
        
        coverage_metrics = {
            'Item_Coverage': item_coverage,
            'User_Coverage': user_coverage,
            'Covered_Items': len(all_recommended),
            'Total_Items': total_items
        }
        
        print(f"物品覆盖率: {item_coverage:.4f} ({len(all_recommended)}/{total_items})")
        print(f"用户覆盖率: {user_coverage:.4f}")
        
        print("\n💡 覆盖率意义:")
        print("• 衡量推荐系统对物品库的利用程度")
        print("• 避免热门物品垄断推荐结果")
        print("• 帮助长尾物品获得曝光机会")
        
        return coverage_metrics
    
    def generate_evaluation_report(self, all_metrics):
        """
        生成综合评估报告
        """
        print("\n" + "="*60)
        print("📋 推荐系统综合评估报告")
        print("="*60)
        
        print("\n🎯 核心指标总结:")
        print(f"• 预测准确性 (RMSE): {all_metrics.get('RMSE', 'N/A')}")
        print(f"• 推荐精确率 (P@10): {all_metrics.get('Precision@10', 'N/A')}")
        print(f"• 推荐召回率 (R@10): {all_metrics.get('Recall@10', 'N/A')}")
        print(f"• 命中率 (HR@10): {all_metrics.get('Hit_Rate@10', 'N/A')}")
        print(f"• 物品多样性: {all_metrics.get('Item_Diversity', 'N/A')}")
        print(f"• 物品覆盖率: {all_metrics.get('Item_Coverage', 'N/A')}")
        
        print("\n📊 性能等级评估:")
        
        # RMSE评估
        rmse = all_metrics.get('RMSE', float('inf'))
        if rmse < 0.8:
            print("• 预测准确性: 🟢 优秀")
        elif rmse < 1.0:
            print("• 预测准确性: 🟡 良好")
        else:
            print("• 预测准确性: 🔴 需要改进")
        
        # Precision评估
        precision = all_metrics.get('Precision@10', 0)
        if precision > 0.2:
            print("• 推荐相关性: 🟢 优秀")
        elif precision > 0.1:
            print("• 推荐相关性: 🟡 良好")
        else:
            print("• 推荐相关性: 🔴 需要改进")
        
        # Hit Rate评估
        hit_rate = all_metrics.get('Hit_Rate@10', 0)
        if hit_rate > 0.7:
            print("• 用户满意度: 🟢 优秀")
        elif hit_rate > 0.5:
            print("• 用户满意度: 🟡 良好")
        else:
            print("• 用户满意度: 🔴 需要改进")
        
        print("\n🚀 优化建议:")
        print("1. 如果预测准确性不佳，考虑:")
        print("   - 增加更多特征")
        print("   - 调整模型架构")
        print("   - 优化超参数")
        
        print("2. 如果推荐相关性不高，考虑:")
        print("   - 改进负采样策略")
        print("   - 调整召回和排序的平衡")
        print("   - 引入更多上下文信息")
        
        print("3. 如果多样性不足，考虑:")
        print("   - 添加多样性约束")
        print("   - 使用重排序算法")
        print("   - 平衡热门和长尾物品")

def demo_evaluation():
    """演示评估方法"""
    print("🎬 推荐系统评估方法演示")
    print("="*60)
    
    # 模拟数据
    np.random.seed(42)
    
    # 模拟预测准确性数据
    y_true = np.random.uniform(1, 5, 1000)  # 真实评分
    y_pred = y_true + np.random.normal(0, 0.5, 1000)  # 预测评分
    y_pred = np.clip(y_pred, 1, 5)  # 限制在1-5范围内
    
    # 模拟推荐数据
    user_recommendations = {
        f'user_{i}': [f'item_{j}' for j in np.random.choice(100, 10, replace=False)]
        for i in range(50)
    }
    
    user_ground_truth = {
        f'user_{i}': [f'item_{j}' for j in np.random.choice(100, 5, replace=False)]
        for i in range(50)
    }
    
    # 模拟物品特征
    categories = ['Action', 'Comedy', 'Drama', 'Horror', 'Romance']
    item_features = {
        f'item_{i}': {
            'category': np.random.choice(categories),
            'year': np.random.randint(1990, 2024)
        }
        for i in range(100)
    }
    
    # 创建评估器
    evaluator = RecommendationEvaluator()
    
    # 执行评估
    all_metrics = {}
    
    # 1. 预测准确性评估
    accuracy_metrics = evaluator.evaluate_prediction_accuracy(y_true, y_pred)
    all_metrics.update(accuracy_metrics)
    
    # 2. 推荐质量评估
    ranking_metrics = evaluator.evaluate_ranking_quality(
        user_recommendations, user_ground_truth, k_values=[5, 10, 20]
    )
    all_metrics.update(ranking_metrics)
    
    # 3. 多样性评估
    diversity_metrics = evaluator.evaluate_diversity(user_recommendations, item_features)
    all_metrics.update(diversity_metrics)
    
    # 4. 覆盖率评估
    coverage_metrics = evaluator.evaluate_coverage(user_recommendations, 100)
    all_metrics.update(coverage_metrics)
    
    # 5. 生成综合报告
    evaluator.generate_evaluation_report(all_metrics)

if __name__ == "__main__":
    demo_evaluation()
