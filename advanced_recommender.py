#!/usr/bin/env python3
"""
高级推荐系统示例 - 使用更真实的数据和特征
展示如何处理更复杂的推荐场景
"""

import numpy as np
import pandas as pd
import tensorflow as tf
import tensorflow_recommenders as tfrs
import matplotlib.pyplot as plt
import seaborn as sns

# 设置随机种子
tf.random.set_seed(42)
np.random.seed(42)

class AdvancedRecommenderModel(tfrs.Model):
    """高级推荐模型 - 包含更多特征"""
    
    def __init__(self, user_vocab, movie_vocab, genre_vocab):
        super().__init__()
        
        self.embedding_dimension = 64
        self.user_vocab = user_vocab
        self.movie_vocab = movie_vocab
        self.genre_vocab = genre_vocab
        
        # 用户嵌入
        self.user_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=user_vocab, mask_token=None),
            tf.keras.layers.Embedding(len(user_vocab) + 1, self.embedding_dimension)
        ])
        
        # 电影嵌入
        self.movie_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=movie_vocab, mask_token=None),
            tf.keras.layers.Embedding(len(movie_vocab) + 1, self.embedding_dimension)
        ])
        
        # 类型嵌入
        self.genre_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=genre_vocab, mask_token=None),
            tf.keras.layers.Embedding(len(genre_vocab) + 1, 16)
        ])
        
        # 评分预测网络
        self.rating_model = tf.keras.Sequential([
            tf.keras.layers.Dense(256, activation="relu"),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(128, activation="relu"),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(64, activation="relu"),
            tf.keras.layers.Dense(1, activation="sigmoid")  # 输出0-1，然后缩放到1-5
        ])
        
        # 任务定义
        self.retrieval_task = tfrs.tasks.Retrieval()
        self.rating_task = tfrs.tasks.Ranking(
            loss=tf.keras.losses.MeanSquaredError(),
            metrics=[tf.keras.metrics.RootMeanSquaredError(), tf.keras.metrics.MeanAbsoluteError()]
        )
    
    def call(self, features):
        user_emb = self.user_embedding(features["user_id"])
        movie_emb = self.movie_embedding(features["movie_title"])
        genre_emb = self.genre_embedding(features["genre"])
        
        # 组合特征
        combined_features = tf.concat([user_emb, movie_emb, genre_emb], axis=1)
        
        # 预测评分 (0-1) 然后缩放到 1-5
        rating_pred = self.rating_model(combined_features) * 4 + 1
        
        return {
            "user_embedding": user_emb,
            "movie_embedding": movie_emb,
            "predicted_rating": rating_pred
        }
    
    def compute_loss(self, features, training=False):
        user_emb = self.user_embedding(features["user_id"])
        movie_emb = self.movie_embedding(features["movie_title"])
        genre_emb = self.genre_embedding(features["genre"])
        
        # 检索损失
        retrieval_loss = self.retrieval_task(
            query_embeddings=user_emb,
            candidate_embeddings=movie_emb
        )
        
        # 评分损失
        combined_features = tf.concat([user_emb, movie_emb, genre_emb], axis=1)
        rating_pred = self.rating_model(combined_features) * 4 + 1
        
        rating_loss = self.rating_task(
            labels=features["user_rating"],
            predictions=rating_pred
        )
        
        return retrieval_loss + rating_loss

def create_realistic_data(num_users=100, num_movies=50, num_ratings=2000):
    """创建更真实的数据集"""
    print(f"创建数据集: {num_users}用户, {num_movies}电影, {num_ratings}评分")
    
    # 用户ID
    users = [f"user_{i}" for i in range(num_users)]
    
    # 电影和类型
    genres = ["Action", "Comedy", "Drama", "Horror", "Romance", "Sci-Fi", "Thriller"]
    movies = []
    movie_genres = []
    
    for i in range(num_movies):
        movies.append(f"movie_{i}")
        movie_genres.append(np.random.choice(genres))
    
    # 生成评分数据
    data = []
    for _ in range(num_ratings):
        user = np.random.choice(users)
        movie_idx = np.random.randint(0, num_movies)
        movie = movies[movie_idx]
        genre = movie_genres[movie_idx]
        
        # 模拟用户偏好 - 某些用户喜欢某些类型
        user_idx = int(user.split('_')[1])
        genre_preference = {
            "Action": 0.1 if user_idx % 7 == 0 else -0.1,
            "Comedy": 0.2 if user_idx % 7 == 1 else -0.1,
            "Drama": 0.15 if user_idx % 7 == 2 else 0,
            "Horror": -0.2 if user_idx % 7 == 3 else 0.1,
            "Romance": 0.3 if user_idx % 7 == 4 else -0.2,
            "Sci-Fi": 0.25 if user_idx % 7 == 5 else 0,
            "Thriller": 0.1 if user_idx % 7 == 6 else 0
        }
        
        # 基础评分 + 类型偏好 + 随机噪声
        base_rating = 3.0
        preference_boost = genre_preference.get(genre, 0)
        noise = np.random.normal(0, 0.5)
        rating = base_rating + preference_boost + noise
        
        # 限制在1-5范围内
        rating = max(1.0, min(5.0, rating))
        
        data.append({
            "user_id": user,
            "movie_title": movie,
            "genre": genre,
            "user_rating": rating
        })
    
    return pd.DataFrame(data), users, movies, genres

def analyze_data(df):
    """分析数据分布"""
    print("\n数据分析:")
    print(f"总评分数: {len(df)}")
    print(f"用户数: {df['user_id'].nunique()}")
    print(f"电影数: {df['movie_title'].nunique()}")
    print(f"类型数: {df['genre'].nunique()}")
    print(f"平均评分: {df['user_rating'].mean():.2f}")
    print(f"评分标准差: {df['user_rating'].std():.2f}")
    
    # 类型分布
    print("\n类型分布:")
    genre_counts = df['genre'].value_counts()
    for genre, count in genre_counts.items():
        print(f"  {genre}: {count}")
    
    # 评分分布
    print("\n评分分布:")
    rating_dist = df['user_rating'].round().value_counts().sort_index()
    for rating, count in rating_dist.items():
        print(f"  {rating}星: {count}")

def train_advanced_model(df, users, movies, genres):
    """训练高级模型"""
    print("\n开始训练高级推荐模型...")
    
    # 转换为TensorFlow数据集
    dataset = tf.data.Dataset.from_tensor_slices({
        "user_id": df["user_id"].values,
        "movie_title": df["movie_title"].values,
        "genre": df["genre"].values,
        "user_rating": df["user_rating"].values.astype(np.float32)
    })
    
    # 数据分割
    dataset = dataset.shuffle(10000, seed=42)
    train_size = int(0.8 * len(df))
    train_ds = dataset.take(train_size).batch(32).cache()
    test_ds = dataset.skip(train_size).batch(16).cache()
    
    # 创建模型
    model = AdvancedRecommenderModel(users, movies, genres)
    model.compile(optimizer=tf.keras.optimizers.Adam(learning_rate=0.001))
    
    # 训练
    print("训练中...")
    history = model.fit(
        train_ds,
        epochs=10,
        validation_data=test_ds,
        verbose=1
    )
    
    # 评估
    print("\n最终评估:")
    metrics = model.evaluate(test_ds, return_dict=True)
    for key, value in metrics.items():
        print(f"  {key}: {value:.3f}")
    
    return model, history

def make_advanced_recommendations(model, user_id, df, k=5):
    """生成高级推荐"""
    print(f"\n为用户 {user_id} 生成个性化推荐:")
    
    # 获取用户历史
    user_history = df[df['user_id'] == user_id]
    if len(user_history) > 0:
        print(f"用户历史评分 ({len(user_history)}部电影):")
        for _, row in user_history.head(3).iterrows():
            print(f"  {row['movie_title']} ({row['genre']}) - {row['user_rating']:.1f}星")
        if len(user_history) > 3:
            print(f"  ... 还有{len(user_history)-3}部电影")
    
    # 获取用户嵌入
    user_input = tf.constant([user_id])
    user_emb = model.user_embedding(user_input)
    
    # 计算与所有电影的相似度
    movie_input = tf.constant(model.movie_vocab)
    movie_embs = model.movie_embedding(movie_input)
    
    scores = tf.linalg.matmul(user_emb, movie_embs, transpose_b=True)
    _, top_indices = tf.nn.top_k(scores[0], k=k)
    
    print(f"\nTop-{k} 推荐:")
    for i, idx in enumerate(top_indices.numpy()):
        movie = model.movie_vocab[idx]
        score = scores[0][idx].numpy()
        
        # 获取电影类型
        movie_info = df[df['movie_title'] == movie].iloc[0] if len(df[df['movie_title'] == movie]) > 0 else None
        genre = movie_info['genre'] if movie_info is not None else "Unknown"
        
        print(f"{i+1}. {movie} ({genre}) - 相似度: {score:.3f}")

def main():
    """主函数"""
    print("=" * 60)
    print("TensorFlow Recommenders 高级推荐系统示例")
    print("=" * 60)
    
    # 创建数据
    df, users, movies, genres = create_realistic_data(
        num_users=50, num_movies=30, num_ratings=1000
    )
    
    # 分析数据
    analyze_data(df)
    
    # 训练模型
    model, history = train_advanced_model(df, users, movies, genres)
    
    # 生成推荐
    sample_users = ["user_0", "user_10", "user_25"]
    for user in sample_users:
        make_advanced_recommendations(model, user, df, k=5)
    
    print("\n" + "=" * 60)
    print("高级推荐系统演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
