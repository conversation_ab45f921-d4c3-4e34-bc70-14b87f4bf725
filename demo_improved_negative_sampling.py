#!/usr/bin/env python3
"""
改进负样本采集策略演示

快速演示改进的负样本采集策略效果：
1. Pointwise训练 (正负样本比例1:2)
2. 热门偏差修正 (训练时使用 cos(a,bi) - log(pi))
3. 推理时还原 (推理时使用原始 cos(a,bi))
"""

import sys
import os
import numpy as np
import pandas as pd
import tensorflow as tf

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from movielens_two_tower import (
    MovieLensDataLoader, 
    TwoTowerModel,
    compute_item_popularity,
    create_pointwise_dataset,
    create_dataset,
    recall_and_rank
)

def demo_improved_negative_sampling():
    """演示改进的负样本采集策略"""
    print("🎬 改进负样本采集策略演示")
    print("="*60)
    
    # 1. 加载数据 (使用小样本)
    print("1. 加载MovieLens数据...")
    loader = MovieLensDataLoader()
    if not loader.load_data():
        print("❌ 数据加载失败！")
        return
    
    df = loader.preprocess_data()
    
    # 使用小样本进行演示 (5000条记录)
    sample_df = df.sample(n=5000, random_state=42)
    vocabs = loader.get_vocabularies(sample_df)
    
    print(f"✅ 数据加载成功 (演示样本)")
    print(f"   用户数: {len(vocabs['user_id'])}")
    print(f"   电影数: {len(vocabs['movie_id'])}")
    print(f"   交互数: {len(sample_df)}")
    
    # 2. 分析物品流行度分布
    print("\n2. 分析物品流行度分布...")
    item_popularity, item_log_prob = compute_item_popularity(sample_df)
    
    # 按流行度排序
    sorted_items = sorted(item_popularity.items(), key=lambda x: x[1], reverse=True)
    
    print(f"✅ 流行度分析完成")
    print(f"   最热门电影: ID={sorted_items[0][0]}, 流行度={sorted_items[0][1]:.6f}")
    print(f"   中等热门电影: ID={sorted_items[len(sorted_items)//2][0]}, 流行度={sorted_items[len(sorted_items)//2][1]:.6f}")
    print(f"   最冷门电影: ID={sorted_items[-1][0]}, 流行度={sorted_items[-1][1]:.6f}")
    print(f"   热门/冷门比例: {sorted_items[0][1] / sorted_items[-1][1]:.1f}:1")
    
    # 3. 对比传统方法和改进方法
    print("\n3. 对比训练方法...")
    
    # 划分训练测试集
    train_df = sample_df.sample(n=4000, random_state=42)
    test_df = sample_df.drop(train_df.index)
    
    train_popularity, train_log_prob = compute_item_popularity(train_df)
    
    print(f"   训练集: {len(train_df)}条记录")
    print(f"   测试集: {len(test_df)}条记录")
    
    # 3.1 传统方法 (pairwise训练)
    print("\n   🔸 传统方法 (Pairwise训练):")
    try:
        traditional_train_ds = create_dataset(train_df, batch_size=64)
        traditional_test_ds = create_dataset(test_df, batch_size=32)
        
        traditional_model = TwoTowerModel(vocabs, embedding_dim=32)
        traditional_model.compile(optimizer=tf.keras.optimizers.Adam(learning_rate=0.001))
        
        print("     🚀 训练传统模型 (2个epoch)...")
        traditional_history = traditional_model.fit(
            traditional_train_ds,
            validation_data=traditional_test_ds,
            epochs=2,
            verbose=0
        )
        
        print(f"     ✅ 传统方法训练完成")
        print(f"        最终训练损失: {traditional_history.history['loss'][-1]:.4f}")
        print(f"        最终验证损失: {traditional_history.history['val_loss'][-1]:.4f}")
        
    except Exception as e:
        print(f"     ❌ 传统方法训练失败: {e}")
        traditional_model = None
    
    # 3.2 改进方法 (pointwise训练 + 热门偏差修正)
    print("\n   🔹 改进方法 (Pointwise + 热门偏差修正):")
    try:
        improved_train_ds = create_pointwise_dataset(
            train_df, vocabs, train_popularity, train_log_prob,
            neg_ratio=2, batch_size=64
        )
        improved_test_ds = create_dataset(test_df, batch_size=32)
        
        improved_model = TwoTowerModel(vocabs, embedding_dim=32)
        improved_model.compile(optimizer=tf.keras.optimizers.Adam(learning_rate=0.001))
        
        print("     🚀 训练改进模型 (2个epoch)...")
        improved_history = improved_model.fit(
            improved_train_ds,
            validation_data=improved_test_ds,
            epochs=2,
            verbose=0
        )
        
        print(f"     ✅ 改进方法训练完成")
        print(f"        最终训练损失: {improved_history.history['loss'][-1]:.4f}")
        print(f"        最终验证损失: {improved_history.history['val_loss'][-1]:.4f}")
        
    except Exception as e:
        print(f"     ❌ 改进方法训练失败: {e}")
        improved_model = None
    
    # 4. 推荐效果对比
    if traditional_model and improved_model:
        print("\n4. 推荐效果对比...")
        
        # 选择一个测试用户
        test_user = test_df.iloc[0]
        user_features = {
            'user_id': test_user['user_id'],
            'gender': test_user['gender'],
            'age': test_user['age'],
            'occupation': test_user['occupation']
        }
        
        # 准备候选电影 (包含不同流行度的电影)
        candidate_movies = []
        test_movies = test_df[['movie_id', 'main_genre', 'year']].drop_duplicates()
        
        # 选择不同流行度的电影作为候选
        hot_movies = [item[0] for item in sorted_items[:3]]  # 热门电影
        cold_movies = [item[0] for item in sorted_items[-3:]]  # 冷门电影
        
        for movie_id in hot_movies + cold_movies:
            movie_info = test_movies[test_movies['movie_id'] == movie_id]
            if not movie_info.empty:
                movie_row = movie_info.iloc[0]
                candidate_movies.append({
                    'movie_id': movie_row['movie_id'],
                    'main_genre': movie_row['main_genre'],
                    'year': movie_row['year']
                })
        
        print(f"   测试用户: {user_features['user_id']}")
        print(f"   候选电影: {len(candidate_movies)}部 (包含热门和冷门电影)")
        
        # 4.1 传统方法推荐
        print("\n   🔸 传统方法推荐结果:")
        try:
            traditional_recs = recall_and_rank(
                traditional_model, user_features, candidate_movies, 
                k_recall=len(candidate_movies), k_rank=3
            )
            
            for i, rec in enumerate(traditional_recs[:3]):
                movie_id = rec['movie_info']['movie_id']
                popularity = item_popularity.get(movie_id, 0)
                print(f"     {i+1}. 电影{movie_id} (流行度:{popularity:.6f}, 相似度:{rec['similarity']:.4f})")
                
        except Exception as e:
            print(f"     ❌ 传统方法推荐失败: {e}")
        
        # 4.2 改进方法推荐
        print("\n   🔹 改进方法推荐结果:")
        try:
            improved_recs = recall_and_rank(
                improved_model, user_features, candidate_movies, 
                k_recall=len(candidate_movies), k_rank=3
            )
            
            for i, rec in enumerate(improved_recs[:3]):
                movie_id = rec['movie_info']['movie_id']
                popularity = item_popularity.get(movie_id, 0)
                print(f"     {i+1}. 电影{movie_id} (流行度:{popularity:.6f}, 相似度:{rec['similarity']:.4f})")
                
        except Exception as e:
            print(f"     ❌ 改进方法推荐失败: {e}")
    
    # 5. 总结改进效果
    print("\n5. 改进效果总结")
    print("="*60)
    print("✅ 实现的改进:")
    print("   🎯 Pointwise训练: 支持1:2正负样本比例")
    print("   🎯 热门偏差修正: 训练时使用 cos(a,bi) - log(pi)")
    print("   🎯 推理时还原: 推理时使用原始 cos(a,bi)")
    print("   🎯 流行度感知: 基于点击次数的负样本采样")
    
    print("\n📈 预期效果:")
    print("   • 减少热门偏差，提升长尾物品曝光")
    print("   • 提高推荐多样性和个性化程度")
    print("   • 保持推理效率，无额外计算开销")
    print("   • 兼容现有系统，支持平滑升级")
    
    print("\n🚀 部署建议:")
    print("   1. 先在小流量上A/B测试验证效果")
    print("   2. 监控推荐多样性和用户满意度指标")
    print("   3. 根据业务需求调整正负样本比例")
    print("   4. 定期更新物品流行度统计")

if __name__ == "__main__":
    demo_improved_negative_sampling()
