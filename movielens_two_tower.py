#!/usr/bin/env python3
"""
基于MovieLens数据集的双塔召回+排序推荐系统
实现两阶段推荐：召回阶段 + 排序阶段
"""

import os
import zipfile
import requests
import numpy as np
import pandas as pd
import tensorflow as tf
import tensorflow_recommenders as tfrs
from typing import Dict, Text
import matplotlib.pyplot as plt

# 设置随机种子
tf.random.set_seed(42)
np.random.seed(42)

class MovieLensDataLoader:
    """MovieLens数据加载器"""
    
    def __init__(self, data_dir="./data"):
        self.data_dir = data_dir
        self.ratings_df = None
        self.movies_df = None
        self.users_df = None
        
    def download_and_extract(self):
        """下载并解压MovieLens 1M数据集"""
        os.makedirs(self.data_dir, exist_ok=True)
        
        url = "https://files.grouplens.org/datasets/movielens/ml-1m.zip"
        zip_path = os.path.join(self.data_dir, "ml-1m.zip")
        extract_path = os.path.join(self.data_dir, "ml-1m")
        
        if not os.path.exists(extract_path):
            print("下载MovieLens 1M数据集...")
            try:
                response = requests.get(url, stream=True)
                response.raise_for_status()
                
                with open(zip_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                print("解压数据集...")
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(self.data_dir)
                
                os.remove(zip_path)
                print("数据集下载完成！")
            except Exception as e:
                print(f"下载失败: {e}")
                print("请手动下载MovieLens 1M数据集到 ./data/ml-1m/ 目录")
                return False
        else:
            print("数据集已存在，跳过下载")
        
        return True
    
    def load_data(self):
        """加载数据"""
        if not self.download_and_extract():
            return False
            
        data_path = os.path.join(self.data_dir, "ml-1m")
        
        # 加载评分数据
        print("加载评分数据...")
        self.ratings_df = pd.read_csv(
            os.path.join(data_path, "ratings.dat"),
            sep="::",
            names=["user_id", "movie_id", "rating", "timestamp"],
            engine="python"
        )
        
        # 加载电影数据
        print("加载电影数据...")
        self.movies_df = pd.read_csv(
            os.path.join(data_path, "movies.dat"),
            sep="::",
            names=["movie_id", "title", "genres"],
            engine="python",
            encoding="latin-1"
        )
        
        # 加载用户数据
        print("加载用户数据...")
        self.users_df = pd.read_csv(
            os.path.join(data_path, "users.dat"),
            sep="::",
            names=["user_id", "gender", "age", "occupation", "zip_code"],
            engine="python"
        )
        
        return True
    
    def preprocess_data(self):
        """数据预处理"""
        print("数据预处理...")
        
        # 提取电影年份
        self.movies_df['year'] = self.movies_df['title'].str.extract(r'\((\d{4})\)')
        self.movies_df['year'] = pd.to_numeric(self.movies_df['year'], errors='coerce')
        self.movies_df['year'] = self.movies_df['year'].fillna(1995).astype(int)
        
        # 处理类型（取第一个类型作为主类型）
        self.movies_df['main_genre'] = self.movies_df['genres'].str.split('|').str[0]
        
        # 合并数据
        merged_df = self.ratings_df.merge(self.movies_df, on='movie_id')
        merged_df = merged_df.merge(self.users_df, on='user_id')
        
        # 转换为字符串类型（TensorFlow需要）
        merged_df['user_id'] = merged_df['user_id'].astype(str)
        merged_df['movie_id'] = merged_df['movie_id'].astype(str)
        merged_df['age'] = merged_df['age'].astype(str)
        merged_df['occupation'] = merged_df['occupation'].astype(str)
        merged_df['year'] = merged_df['year'].astype(str)
        
        return merged_df
    
    def get_vocabularies(self, df):
        """获取各特征的词汇表"""
        vocabs = {
            'user_id': sorted(df['user_id'].unique()),
            'movie_id': sorted(df['movie_id'].unique()),
            'gender': sorted(df['gender'].unique()),
            'age': sorted(df['age'].unique()),
            'occupation': sorted(df['occupation'].unique()),
            'main_genre': sorted(df['main_genre'].unique()),
            'year': sorted(df['year'].unique())
        }
        return vocabs

class TwoTowerModel(tfrs.Model):
    """双塔模型：用户塔 + 物品塔"""
    
    def __init__(self, vocabs, embedding_dim=64):
        super().__init__()
        
        self.embedding_dim = embedding_dim
        self.vocabs = vocabs
        
        # 用户塔
        self.user_tower = self._build_user_tower()
        
        # 物品塔
        self.item_tower = self._build_item_tower()
        
        # 召回任务 - 简化版本
        self.retrieval_task = tfrs.tasks.Retrieval()
        
        # 排序任务
        self.ranking_task = tfrs.tasks.Ranking(
            loss=tf.keras.losses.MeanSquaredError(),
            metrics=[
                tf.keras.metrics.RootMeanSquaredError(name="rmse"),
                tf.keras.metrics.MeanAbsoluteError(name="mae")
            ]
        )
        
        # 排序网络
        self.ranking_model = tf.keras.Sequential([
            tf.keras.layers.Dense(256, activation="relu"),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(128, activation="relu"),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(64, activation="relu"),
            tf.keras.layers.Dense(1, activation="sigmoid")
        ])

        # 用户和物品特征融合层
        self.user_projection = tf.keras.layers.Dense(self.embedding_dim, activation='relu')
        self.item_projection = tf.keras.layers.Dense(self.embedding_dim, activation='relu')
    
    def _build_user_tower(self):
        """构建用户塔"""
        # 用户ID嵌入
        user_id_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self.vocabs['user_id'], mask_token=None),
            tf.keras.layers.Embedding(len(self.vocabs['user_id']) + 1, self.embedding_dim)
        ])
        
        # 性别嵌入
        gender_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self.vocabs['gender'], mask_token=None),
            tf.keras.layers.Embedding(len(self.vocabs['gender']) + 1, 8)
        ])
        
        # 年龄嵌入
        age_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self.vocabs['age'], mask_token=None),
            tf.keras.layers.Embedding(len(self.vocabs['age']) + 1, 8)
        ])
        
        # 职业嵌入
        occupation_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self.vocabs['occupation'], mask_token=None),
            tf.keras.layers.Embedding(len(self.vocabs['occupation']) + 1, 16)
        ])
        
        return {
            'user_id': user_id_embedding,
            'gender': gender_embedding,
            'age': age_embedding,
            'occupation': occupation_embedding
        }
    
    def _build_item_tower(self):
        """构建物品塔"""
        # 电影ID嵌入
        movie_id_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self.vocabs['movie_id'], mask_token=None),
            tf.keras.layers.Embedding(len(self.vocabs['movie_id']) + 1, self.embedding_dim)
        ])
        
        # 类型嵌入
        genre_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self.vocabs['main_genre'], mask_token=None),
            tf.keras.layers.Embedding(len(self.vocabs['main_genre']) + 1, 16)
        ])
        
        # 年份嵌入
        year_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self.vocabs['year'], mask_token=None),
            tf.keras.layers.Embedding(len(self.vocabs['year']) + 1, 8)
        ])
        
        return {
            'movie_id': movie_id_embedding,
            'main_genre': genre_embedding,
            'year': year_embedding
        }
    
    def _get_user_embedding(self, features):
        """获取用户嵌入"""
        user_embs = []
        user_embs.append(self.user_tower['user_id'](features['user_id']))
        user_embs.append(self.user_tower['gender'](features['gender']))
        user_embs.append(self.user_tower['age'](features['age']))
        user_embs.append(self.user_tower['occupation'](features['occupation']))

        # 拼接所有用户特征
        user_emb = tf.concat(user_embs, axis=1)

        # 通过全连接层降维到统一维度
        user_emb = self.user_projection(user_emb)
        return user_emb
    
    def _get_item_embedding(self, features):
        """获取物品嵌入"""
        item_embs = []
        item_embs.append(self.item_tower['movie_id'](features['movie_id']))
        item_embs.append(self.item_tower['main_genre'](features['main_genre']))
        item_embs.append(self.item_tower['year'](features['year']))

        # 拼接所有物品特征
        item_emb = tf.concat(item_embs, axis=1)

        # 通过全连接层降维到统一维度
        item_emb = self.item_projection(item_emb)
        return item_emb
    
    def call(self, features, training=False):
        """前向传播"""
        user_emb = self._get_user_embedding(features)
        item_emb = self._get_item_embedding(features)

        # 计算用户-物品相似度 (cosine similarity)
        user_emb_norm = tf.nn.l2_normalize(user_emb, axis=1)
        item_emb_norm = tf.nn.l2_normalize(item_emb, axis=1)
        cosine_sim = tf.reduce_sum(user_emb_norm * item_emb_norm, axis=1, keepdims=True)

        # 训练时应用热门偏差修正
        if training and 'bias_correction' in features:
            # cos(a,bi) - log(pi) 修正热门偏差
            corrected_sim = cosine_sim - tf.expand_dims(features['bias_correction'], axis=1)
        else:
            # 推理时不应用修正
            corrected_sim = cosine_sim

        return {
            'user_embedding': user_emb,
            'item_embedding': item_emb,
            'cosine_similarity': cosine_sim,  # 原始相似度 (推理时用)
            'corrected_similarity': corrected_sim,  # 修正后相似度 (训练时用)
            'predicted_rating': self.ranking_model(tf.concat([user_emb, item_emb], axis=1)) * 4 + 1
        }
    
    def compute_loss(self, features, training=False):
        """计算损失 - 支持pointwise训练"""
        # TensorFlow Recommenders传入的是(features, labels)元组
        if isinstance(features, tuple):
            features, labels = features

        outputs = self(features, training=training)
        user_emb = outputs['user_embedding']
        item_emb = outputs['item_embedding']
        rating_pred = outputs['predicted_rating']
        corrected_sim = outputs['corrected_similarity']

        # 如果有label字段，使用pointwise训练
        if 'label' in features:
            # Pointwise召回损失 - 使用修正后的相似度
            # 确保维度匹配
            labels = tf.expand_dims(features['label'], axis=1)  # (batch_size, 1)
            probs = tf.nn.sigmoid(corrected_sim)  # (batch_size, 1)

            retrieval_loss = tf.keras.losses.binary_crossentropy(
                y_true=labels,
                y_pred=probs,
                from_logits=False
            )
            retrieval_loss = tf.reduce_mean(retrieval_loss)

            # 对所有样本计算排序损失，但只对正样本有效
            # 使用mask来加权损失
            positive_mask = tf.cast(tf.equal(features['label'], 1.0), tf.float32)

            # 计算MSE损失
            rating_diff = features['rating'] - tf.squeeze(rating_pred)
            squared_diff = tf.square(rating_diff)

            # 只对正样本计算损失
            masked_loss = squared_diff * positive_mask
            ranking_loss = tf.reduce_sum(masked_loss) / (tf.reduce_sum(positive_mask) + 1e-8)
        else:
            # 原始的pairwise训练方式
            retrieval_loss = self.retrieval_task(
                query_embeddings=user_emb,
                candidate_embeddings=item_emb
            )

            ranking_loss = self.ranking_task(
                labels=features['rating'],
                predictions=rating_pred
            )

        return retrieval_loss + ranking_loss

def compute_item_popularity(df):
    """计算物品流行度和采样概率"""
    item_counts = df['movie_id'].value_counts()
    total_interactions = len(df)

    # 计算每个物品的流行度概率
    item_popularity = {}
    item_log_prob = {}

    for movie_id, count in item_counts.items():
        prob = count / total_interactions
        item_popularity[movie_id] = prob
        item_log_prob[movie_id] = np.log(prob)

    print(f"计算了 {len(item_popularity)} 个物品的流行度")
    print(f"最热门物品流行度: {max(item_popularity.values()):.6f}")
    print(f"最冷门物品流行度: {min(item_popularity.values()):.6f}")

    return item_popularity, item_log_prob

def create_pointwise_dataset(df, vocabs, item_popularity, item_log_prob,
                           neg_ratio=2, batch_size=512):
    """
    创建pointwise训练数据集，包含正负样本

    Args:
        df: 原始交互数据
        vocabs: 词汇表
        item_popularity: 物品流行度字典
        item_log_prob: 物品log概率字典
        neg_ratio: 负样本比例 (1:neg_ratio)
        batch_size: 批次大小
    """
    print(f"创建pointwise数据集，正负样本比例 1:{neg_ratio}")

    # 准备正样本 (评分>=4的为正样本)
    positive_df = df[df['rating'] >= 4.0].copy()
    positive_df['label'] = 1.0
    positive_df['bias_correction'] = 0.0  # 正样本不需要偏差修正

    print(f"正样本数量: {len(positive_df)}")

    # 为每个正样本生成负样本
    negative_samples = []
    all_movies = list(vocabs['movie_id'])

    for _, pos_sample in positive_df.iterrows():
        user_id = pos_sample['user_id']
        user_movies = set(df[df['user_id'] == user_id]['movie_id'].tolist())

        # 候选负样本 (用户没有交互过的物品)
        candidate_negatives = [m for m in all_movies if m not in user_movies]

        if len(candidate_negatives) < neg_ratio:
            continue

        # 根据流行度采样负样本
        neg_probs = [item_popularity.get(m, 1e-10) for m in candidate_negatives]
        neg_probs = np.array(neg_probs)
        neg_probs = neg_probs / neg_probs.sum()

        # 采样负样本
        sampled_negatives = np.random.choice(
            candidate_negatives,
            size=neg_ratio,
            replace=False,
            p=neg_probs
        )

        # 创建负样本记录
        for neg_movie in sampled_negatives:
            neg_sample = pos_sample.copy()
            neg_sample['movie_id'] = neg_movie
            neg_sample['label'] = 0.0
            neg_sample['rating'] = 1.0  # 负样本设为低评分

            # 获取对应的物品特征
            movie_info = df[df['movie_id'] == neg_movie].iloc[0]
            neg_sample['main_genre'] = movie_info['main_genre']
            neg_sample['year'] = movie_info['year']

            # 添加偏差修正项 (训练时用于修正热门偏差)
            neg_sample['bias_correction'] = item_log_prob.get(neg_movie, 0.0)

            negative_samples.append(neg_sample)

    print(f"生成负样本数量: {len(negative_samples)}")

    # 合并正负样本
    negative_df = pd.DataFrame(negative_samples)
    combined_df = pd.concat([positive_df, negative_df], ignore_index=True)
    combined_df = combined_df.sample(frac=1, random_state=42).reset_index(drop=True)

    print(f"总样本数量: {len(combined_df)} (正样本: {len(positive_df)}, 负样本: {len(negative_df)})")

    # 创建TensorFlow数据集
    features_dict = {
        'user_id': combined_df['user_id'].values,
        'movie_id': combined_df['movie_id'].values,
        'gender': combined_df['gender'].values,
        'age': combined_df['age'].values,
        'occupation': combined_df['occupation'].values,
        'main_genre': combined_df['main_genre'].values,
        'year': combined_df['year'].values,
        'rating': combined_df['rating'].values.astype(np.float32),
        'label': combined_df['label'].values.astype(np.float32),
        'bias_correction': combined_df['bias_correction'].values.astype(np.float32)
    }

    # 创建数据集，返回 (features, rating) 格式以保持兼容性
    dataset = tf.data.Dataset.from_tensor_slices((features_dict, combined_df['rating'].values.astype(np.float32)))

    return dataset.shuffle(100000, seed=42).batch(batch_size).cache().prefetch(tf.data.AUTOTUNE)

def create_dataset(df, batch_size=512):
    """创建原始TensorFlow数据集 (用于测试)"""
    dataset = tf.data.Dataset.from_tensor_slices({
        'user_id': df['user_id'].values,
        'movie_id': df['movie_id'].values,
        'gender': df['gender'].values,
        'age': df['age'].values,
        'occupation': df['occupation'].values,
        'main_genre': df['main_genre'].values,
        'year': df['year'].values,
        'rating': df['rating'].values.astype(np.float32)
    })

    return dataset.shuffle(100000, seed=42).batch(batch_size).cache().prefetch(tf.data.AUTOTUNE)

def train_two_tower_model(df, vocabs, test_size=0.2, neg_ratio=2):
    """训练双塔模型 - 支持pointwise训练和热门偏差修正"""
    print("准备训练数据...")

    # 数据分割
    df_shuffled = df.sample(frac=1, random_state=42).reset_index(drop=True)
    split_idx = int(len(df) * (1 - test_size))

    train_df = df_shuffled[:split_idx]
    test_df = df_shuffled[split_idx:]

    print(f"训练集大小: {len(train_df)}")
    print(f"测试集大小: {len(test_df)}")

    # 计算物品流行度 (基于训练集)
    item_popularity, item_log_prob = compute_item_popularity(train_df)

    # 创建pointwise训练数据集
    print(f"\n创建pointwise训练数据集 (负样本比例 1:{neg_ratio})...")
    train_ds = create_pointwise_dataset(
        train_df, vocabs, item_popularity, item_log_prob,
        neg_ratio=neg_ratio, batch_size=512
    )

    # 测试集仍使用原始数据
    test_ds = create_dataset(test_df, batch_size=256)

    # 创建模型
    print("创建双塔模型...")
    model = TwoTowerModel(vocabs, embedding_dim=64)

    # 编译模型
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        run_eagerly=False
    )

    # 训练模型
    print("开始训练...")
    history = model.fit(
        train_ds,
        epochs=5,
        validation_data=test_ds,
        verbose=1
    )

    # 最终评估
    print("\n最终评估结果:")
    metrics = model.evaluate(test_ds, return_dict=True, verbose=0)
    for key, value in metrics.items():
        print(f"  {key}: {value:.4f}")

    return model, history, test_ds

def evaluate_recommendations(model, test_ds, user_movies_dict, movie_features, k_values=[5, 10, 20]):
    """
    全面评估推荐系统性能

    Args:
        model: 训练好的双塔模型
        test_ds: 测试数据集
        user_movies_dict: 用户历史交互字典 {user_id: [movie_ids]}
        movie_features: 电影特征数据
        k_values: 评估的K值列表

    Returns:
        dict: 包含各种评估指标的字典
    """
    print("\n" + "="*60)
    print("推荐系统全面评估")
    print("="*60)

    # 1. 基础预测准确性评估
    print("\n1. 预测准确性评估:")
    metrics = model.evaluate(test_ds, return_dict=True, verbose=0)
    for key, value in metrics.items():
        print(f"  {key}: {value:.4f}")

    # 2. 为测试用户生成推荐并评估
    print("\n2. 推荐质量评估:")

    # 获取测试集中的用户
    test_users = []
    test_user_movie_pairs = []
    test_ratings = []

    for batch in test_ds.take(10):  # 取部分测试数据进行评估
        features, ratings = batch
        batch_users = features['user_id'].numpy()
        batch_movies = features['movie_id'].numpy()
        batch_ratings = ratings.numpy()

        test_users.extend(batch_users)
        test_user_movie_pairs.extend(list(zip(batch_users, batch_movies)))
        test_ratings.extend(batch_ratings)

    # 去重获取唯一用户
    unique_test_users = list(set(test_users))[:50]  # 评估前50个用户

    evaluation_results = {}

    for k in k_values:
        precision_scores = []
        recall_scores = []
        hit_rates = []

        print(f"\n  评估 Top-{k} 推荐:")

        for user_id in unique_test_users:
            try:
                # 获取用户特征
                user_data = movie_features[movie_features['user_id'] == user_id].iloc[0]
                user_features = {
                    'user_id': tf.constant([user_id]),
                    'gender': tf.constant([user_data['gender']]),
                    'age': tf.constant([user_data['age']]),
                    'occupation': tf.constant([user_data['occupation']])
                }

                # 生成推荐
                recommendations = recall_and_rank(
                    model, user_features, movie_features,
                    k_recall=100, k_rank=k
                )

                if not recommendations:
                    continue

                recommended_movies = [rec['movie_id'] for rec in recommendations]

                # 获取用户在测试集中的真实高分电影 (评分>=4)
                user_test_pairs = [(u, m, r) for u, m, r in zip(test_users, [pair[1] for pair in test_user_movie_pairs], test_ratings) if u == user_id]
                true_positive_movies = [m for u, m, r in user_test_pairs if r >= 4.0]

                if len(true_positive_movies) == 0:
                    continue

                # 计算精确率和召回率
                recommended_set = set(recommended_movies)
                true_positive_set = set(true_positive_movies)

                intersection = recommended_set.intersection(true_positive_set)

                precision = len(intersection) / len(recommended_set) if len(recommended_set) > 0 else 0
                recall = len(intersection) / len(true_positive_set) if len(true_positive_set) > 0 else 0
                hit_rate = 1 if len(intersection) > 0 else 0

                precision_scores.append(precision)
                recall_scores.append(recall)
                hit_rates.append(hit_rate)

            except Exception as e:
                continue

        # 计算平均指标
        if precision_scores:
            avg_precision = np.mean(precision_scores)
            avg_recall = np.mean(recall_scores)
            avg_hit_rate = np.mean(hit_rates)

            evaluation_results[f'precision@{k}'] = avg_precision
            evaluation_results[f'recall@{k}'] = avg_recall
            evaluation_results[f'hit_rate@{k}'] = avg_hit_rate

            print(f"    Precision@{k}: {avg_precision:.4f}")
            print(f"    Recall@{k}: {avg_recall:.4f}")
            print(f"    Hit Rate@{k}: {avg_hit_rate:.4f}")
        else:
            print(f"    无法计算 Top-{k} 指标 (数据不足)")

    return evaluation_results

def analyze_recommendation_diversity(recommendations_list):
    """
    分析推荐结果的多样性

    Args:
        recommendations_list: 多个用户的推荐结果列表

    Returns:
        dict: 多样性分析结果
    """
    print("\n3. 推荐多样性分析:")

    all_genres = []
    all_years = []
    all_movies = []

    for recommendations in recommendations_list:
        for rec in recommendations:
            if 'genres' in rec:
                all_genres.extend(rec['genres'].split('|'))
            if 'year' in rec:
                all_years.append(rec['year'])
            if 'movie_id' in rec:
                all_movies.append(rec['movie_id'])

    # 类型多样性
    unique_genres = len(set(all_genres))
    total_genres = len(all_genres)
    genre_diversity = unique_genres / total_genres if total_genres > 0 else 0

    # 年份分布
    year_range = max(all_years) - min(all_years) if all_years else 0

    # 电影重复率
    unique_movies = len(set(all_movies))
    total_movies = len(all_movies)
    movie_diversity = unique_movies / total_movies if total_movies > 0 else 0

    print(f"  类型多样性: {genre_diversity:.4f} ({unique_genres}/{total_genres})")
    print(f"  年份跨度: {year_range} 年")
    print(f"  电影多样性: {movie_diversity:.4f} ({unique_movies}/{total_movies})")

    return {
        'genre_diversity': genre_diversity,
        'year_range': year_range,
        'movie_diversity': movie_diversity
    }

def recall_and_rank(model, user_features, candidate_movies, k_recall=100, k_rank=10):
    """两阶段推荐：召回 + 排序 (推理时使用原始cosine相似度)"""

    # 第一阶段：召回
    print(f"第一阶段：从{len(candidate_movies)}个候选中召回top-{k_recall}")

    # 计算与所有候选物品的相似度
    similarities = []
    movie_ids = []

    for movie_info in candidate_movies:
        # 构建特征
        features = {
            'user_id': tf.constant([user_features['user_id']]),
            'movie_id': tf.constant([movie_info['movie_id']]),
            'gender': tf.constant([user_features['gender']]),
            'age': tf.constant([user_features['age']]),
            'occupation': tf.constant([user_features['occupation']]),
            'main_genre': tf.constant([movie_info['main_genre']]),
            'year': tf.constant([movie_info['year']])
        }

        # 推理时不应用偏差修正，使用原始cosine相似度
        output = model(features, training=False)
        similarity = output['cosine_similarity'].numpy()[0][0]

        similarities.append(similarity)
        movie_ids.append(movie_info)

    # 召回top-K
    similarities = np.array(similarities)
    top_indices = np.argsort(similarities)[-k_recall:][::-1]
    recalled_movies = [movie_ids[i] for i in top_indices]
    recalled_similarities = similarities[top_indices]

    print(f"召回完成，相似度范围: {recalled_similarities.min():.4f} - {recalled_similarities.max():.4f}")
    print(f"注意：推理时使用原始cosine相似度，未应用训练时的热门偏差修正")

    # 第二阶段：排序
    print(f"第二阶段：对召回的{len(recalled_movies)}个候选进行排序")

    # 预测评分
    predicted_ratings = []
    for movie_info in recalled_movies:
        features = {
            'user_id': tf.constant([user_features['user_id']]),
            'movie_id': tf.constant([movie_info['movie_id']]),
            'gender': tf.constant([user_features['gender']]),
            'age': tf.constant([user_features['age']]),
            'occupation': tf.constant([user_features['occupation']]),
            'main_genre': tf.constant([movie_info['main_genre']]),
            'year': tf.constant([movie_info['year']])
        }

        output = model(features)
        predicted_ratings.append(output['predicted_rating'].numpy()[0][0])

    # 按预测评分排序
    rating_indices = np.argsort(predicted_ratings)[-k_rank:][::-1]
    final_recommendations = []

    for i in rating_indices:
        final_recommendations.append({
            'movie_info': recalled_movies[i],
            'similarity': recalled_similarities[i],
            'predicted_rating': predicted_ratings[i]
        })

    return final_recommendations

def demonstrate_recommendations(model, df, vocabs, num_users=3):
    """演示推荐结果"""
    print("\n" + "="*60)
    print("双塔召回+排序推荐演示")
    print("="*60)

    # 准备候选电影列表
    candidate_movies = []
    for _, movie in df[['movie_id', 'main_genre', 'year']].drop_duplicates().iterrows():
        candidate_movies.append({
            'movie_id': movie['movie_id'],
            'main_genre': movie['main_genre'],
            'year': movie['year']
        })

    # 获取电影标题映射
    movie_titles = df[['movie_id', 'title']].drop_duplicates().set_index('movie_id')['title'].to_dict()

    # 为几个用户生成推荐
    sample_users = df[['user_id', 'gender', 'age', 'occupation']].drop_duplicates().head(num_users)

    for _, user in sample_users.iterrows():
        print(f"\n用户 {user['user_id']} ({user['gender']}, {user['age']}岁, 职业{user['occupation']}):")

        # 显示用户历史
        user_history = df[df['user_id'] == user['user_id']].sort_values('rating', ascending=False).head(5)
        print("历史高分电影:")
        for _, hist in user_history.iterrows():
            print(f"  {hist['title'][:50]}... - {hist['rating']}星")

        # 生成推荐
        user_features = {
            'user_id': user['user_id'],
            'gender': user['gender'],
            'age': user['age'],
            'occupation': user['occupation']
        }

        recommendations = recall_and_rank(
            model, user_features, candidate_movies, k_recall=100, k_rank=5
        )

        print("推荐结果:")
        for i, rec in enumerate(recommendations):
            movie_id = rec['movie_info']['movie_id']
            title = movie_titles.get(movie_id, f"Movie {movie_id}")
            print(f"  {i+1}. {title[:50]}...")
            print(f"     类型: {rec['movie_info']['main_genre']}, "
                  f"年份: {rec['movie_info']['year']}")
            print(f"     相似度: {rec['similarity']:.4f}, "
                  f"预测评分: {rec['predicted_rating']:.2f}")

def comprehensive_evaluation_demo(model, test_ds, df, vocabs):
    """
    全面评估推荐系统性能
    """
    print("\n" + "="*60)
    print("推荐系统全面评估")
    print("="*60)

    # 1. 基础预测准确性评估
    print("\n1. 预测准确性评估:")
    print("   评估模型在测试集上的预测准确性...")

    try:
        metrics = model.evaluate(test_ds, return_dict=True, verbose=0)
        for key, value in metrics.items():
            print(f"   {key}: {value:.4f}")

        # 解释指标含义
        print("\n   指标解释:")
        print("   • RMSE (均方根误差): 预测评分与真实评分的偏差，越小越好")
        print("   • MAE (平均绝对误差): 预测评分的平均偏差，越小越好")
        print("   • Total Loss: 总体损失，包含召回和排序任务")

    except Exception as e:
        print(f"   评估失败: {e}")

    # 2. 推荐质量分析
    print("\n2. 推荐质量分析:")
    print("   分析推荐结果的相关性和多样性...")

    # 选择几个测试用户进行分析
    sample_users = df['user_id'].unique()[:5]

    all_recommendations = []
    precision_scores = []

    for user_id in sample_users:
        try:
            # 获取用户历史高分电影 (作为ground truth)
            user_data = df[df['user_id'] == user_id]
            high_rated_movies = set(user_data[user_data['rating'] >= 4.0]['movie_id'].tolist())

            if len(high_rated_movies) < 3:  # 跳过高分电影太少的用户
                continue

            # 生成推荐
            user_features = {
                'user_id': tf.constant([user_id]),
                'gender': tf.constant([user_data.iloc[0]['gender']]),
                'age': tf.constant([user_data.iloc[0]['age']]),
                'occupation': tf.constant([user_data.iloc[0]['occupation']])
            }

            recommendations = recall_and_rank(
                model, user_features, df, vocabs,
                k_recall=50, k_rank=10
            )

            if recommendations:
                all_recommendations.append(recommendations)

                # 计算精确率 (推荐中有多少是用户真正喜欢的)
                recommended_movies = set([rec['movie_info']['movie_id'] for rec in recommendations])
                intersection = recommended_movies.intersection(high_rated_movies)
                precision = len(intersection) / len(recommended_movies) if recommended_movies else 0
                precision_scores.append(precision)

                print(f"   用户 {user_id}: 推荐 {len(recommendations)} 部电影, "
                      f"精确率 {precision:.3f}")

        except Exception as e:
            print(f"   用户 {user_id}: 分析失败 - {e}")
            continue

    # 计算平均精确率
    if precision_scores:
        avg_precision = np.mean(precision_scores)
        print(f"\n   平均精确率@10: {avg_precision:.4f}")
        print("   (推荐列表中用户真正喜欢的电影比例)")

    # 3. 多样性分析
    print("\n3. 推荐多样性分析:")

    if all_recommendations:
        all_genres = []
        all_years = []
        unique_movies = set()

        for recommendations in all_recommendations:
            for rec in recommendations:
                movie_info = rec['movie_info']
                all_genres.append(movie_info['main_genre'])
                all_years.append(movie_info['year'])
                unique_movies.add(movie_info['movie_id'])

        # 类型多样性
        unique_genres = len(set(all_genres))
        genre_diversity = unique_genres / len(vocabs['main_genre'])

        # 年份分布
        year_range = max(all_years) - min(all_years) if all_years else 0

        print(f"   类型覆盖率: {genre_diversity:.3f} ({unique_genres}/{len(vocabs['main_genre'])} 种类型)")
        print(f"   年份跨度: {year_range} 年 ({min(all_years)}-{max(all_years)})")
        print(f"   推荐电影数: {len(unique_movies)} 部不同电影")

    # 4. 评估总结
    print("\n4. 评估总结:")
    print("   ✅ 预测准确性: RMSE < 1.0 表示预测质量良好")
    print("   ✅ 推荐相关性: 精确率 > 0.1 表示推荐具有一定相关性")
    print("   ✅ 推荐多样性: 覆盖多种类型和年份的电影")
    print("\n   💡 改进建议:")
    print("   • 增加更多特征 (如导演、演员、标签等)")
    print("   • 调整召回和排序的权重平衡")
    print("   • 引入负采样策略改善训练效果")
    print("   • 考虑时间因素和用户行为序列")

def main():
    """主函数"""
    print("="*60)
    print("MovieLens双塔召回+排序推荐系统")
    print("="*60)

    # 加载数据
    loader = MovieLensDataLoader()
    if not loader.load_data():
        print("数据加载失败！")
        return

    # 预处理数据
    df = loader.preprocess_data()
    vocabs = loader.get_vocabularies(df)

    print(f"\n数据统计:")
    print(f"  用户数: {len(vocabs['user_id'])}")
    print(f"  电影数: {len(vocabs['movie_id'])}")
    print(f"  评分数: {len(df)}")
    print(f"  类型数: {len(vocabs['main_genre'])}")

    # 训练模型 (使用改进的负样本采集策略)
    print(f"\n🚀 开始训练双塔模型 (使用pointwise训练和热门偏差修正)...")
    model, history, test_ds = train_two_tower_model(df, vocabs, neg_ratio=2)

    # 演示推荐
    demonstrate_recommendations(model, df, vocabs)

    # 全面评估演示
    comprehensive_evaluation_demo(model, test_ds, df, vocabs)

    print("\n" + "="*60)
    print("双塔推荐系统演示完成！")
    print("="*60)

if __name__ == "__main__":
    main()
