# 简单推荐系统 - TensorFlow Recommenders 示例

这个项目展示了如何使用 TensorFlow Recommenders (TFX) 构建一个基本的推荐系统。

## 环境要求

- Python 3.9+
- TensorFlow 2.16.2
- TensorFlow Recommenders 0.7.3
- NumPy 1.26.4
- Pandas 2.3.1

## 项目结构

```
RS/
├── simple_recommender.py  # 主要的推荐系统代码
├── README.md              # 项目说明文档
└── requirements.txt       # 依赖列表（即将创建）
```

## 推荐系统概述

这个示例实现了一个**混合推荐系统**，结合了两种主要的推荐方法：

### 1. 协同过滤 (Collaborative Filtering)
- 基于用户-物品交互数据
- 学习用户和物品的嵌入表示
- 通过计算用户和物品嵌入的相似度来生成推荐

### 2. 评分预测 (Rating Prediction)
- 预测用户对特定物品的评分
- 使用深度神经网络进行回归预测
- 帮助理解用户的偏好强度

## 核心组件

### MovieLensModel 类
- **用户嵌入层**: 将用户ID映射到高维向量空间
- **物品嵌入层**: 将电影ID映射到高维向量空间
- **评分预测网络**: 多层感知机，预测用户对电影的评分
- **检索任务**: 学习用户-物品匹配
- **评分任务**: 学习准确的评分预测

### 损失函数
模型使用组合损失函数：
```
总损失 = α × 检索损失 + β × 评分损失
```

其中：
- **检索损失**: 优化用户-物品匹配的准确性
- **评分损失**: 优化评分预测的准确性
- α, β 是权重参数，可以调节两个任务的重要性

## 使用方法

### 1. 运行基本示例
```bash
python3 simple_recommender.py
```

### 2. 预期输出
程序将显示：
- 模型训练过程
- 评估指标（Top-K准确率、RMSE）
- 为不同用户生成的推荐列表

## 示例数据

当前使用模拟数据：
- 5个用户 (user_1 到 user_5)
- 5部电影 (movie_1 到 movie_5)
- 随机生成的1-5分评分

## 扩展建议

### 1. 使用真实数据
```python
# 可以替换为 MovieLens 数据集
# https://grouplens.org/datasets/movielens/
```

### 2. 添加更多特征
- 电影类型、年份、导演
- 用户年龄、性别、职业
- 时间信息

### 3. 改进模型架构
- 使用更复杂的神经网络
- 添加注意力机制
- 实现序列推荐

### 4. 评估指标
- Precision@K, Recall@K
- NDCG (Normalized Discounted Cumulative Gain)
- 多样性指标

## 推荐系统流程

1. **数据准备**: 收集用户-物品交互数据
2. **特征工程**: 处理用户和物品特征
3. **模型训练**: 使用TFX训练推荐模型
4. **模型评估**: 使用离线指标评估性能
5. **推荐生成**: 为用户生成个性化推荐
6. **在线部署**: 部署模型提供实时推荐服务

## 常见问题

### Q: 为什么使用混合模型？
A: 混合模型结合了检索和排序的优势，既能快速找到相关物品，又能准确预测用户偏好。

### Q: 如何处理冷启动问题？
A: 可以添加内容特征（如电影类型）来处理新用户或新物品的推荐。

### Q: 如何提高推荐质量？
A: 
- 增加训练数据量
- 优化模型架构
- 调整超参数
- 添加更多特征

## 下一步

1. 尝试运行示例代码
2. 理解模型架构和训练过程
3. 实验不同的超参数设置
4. 尝试使用真实数据集
5. 实现更复杂的推荐算法
