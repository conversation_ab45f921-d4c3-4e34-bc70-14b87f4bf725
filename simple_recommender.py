#!/usr/bin/env python3
"""
简单的推荐系统示例 - 使用 TensorFlow Recommenders (TFX)
这个示例展示了如何构建一个基本的协同过滤推荐系统
"""

import os
import tempfile
import numpy as np
import pandas as pd
import tensorflow as tf
import tensorflow_recommenders as tfrs

# 设置随机种子以确保结果可重现
tf.random.set_seed(42)
np.random.seed(42)

print("TensorFlow version:", tf.__version__)
print("TensorFlow Recommenders version:", tfrs.__version__)

class SimpleRecommenderModel(tfrs.Model):
    """简化的推荐模型"""

    def __init__(self):
        super().__init__()

        # 用户和电影的嵌入维度
        embedding_dimension = 32

        # 用户和电影的词汇表
        self._user_vocab = ["user_1", "user_2", "user_3", "user_4", "user_5"]
        self._movie_vocab = ["movie_1", "movie_2", "movie_3", "movie_4", "movie_5"]

        # 用户和电影的嵌入层
        self.user_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self._user_vocab, mask_token=None),
            tf.keras.layers.Embedding(len(self._user_vocab) + 1, embedding_dimension)
        ])

        self.movie_embedding = tf.keras.Sequential([
            tf.keras.layers.StringLookup(vocabulary=self._movie_vocab, mask_token=None),
            tf.keras.layers.Embedding(len(self._movie_vocab) + 1, embedding_dimension)
        ])

        # 评分预测模型
        self.rating_model = tf.keras.Sequential([
            tf.keras.layers.Dense(256, activation="relu"),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(64, activation="relu"),
            tf.keras.layers.Dense(1)
        ])

        # 简化的检索任务 - 不使用复杂的metrics
        self.retrieval_task = tfrs.tasks.Retrieval()

        # 评分任务
        self.rating_task = tfrs.tasks.Ranking(
            loss=tf.keras.losses.MeanSquaredError(),
            metrics=[tf.keras.metrics.RootMeanSquaredError()]
        )
    
    def call(self, features):
        """模型前向传播"""
        user_embeddings = self.user_embedding(features["user_id"])
        positive_movie_embeddings = self.movie_embedding(features["movie_title"])
        
        return {
            "user_embedding": user_embeddings,
            "movie_embedding": positive_movie_embeddings,
            "predicted_rating": self.rating_model(
                tf.concat([user_embeddings, positive_movie_embeddings], axis=1)
            )
        }
    
    def compute_loss(self, features, training=False):
        """计算损失函数"""
        user_embeddings = self.user_embedding(features["user_id"])
        positive_movie_embeddings = self.movie_embedding(features["movie_title"])

        # 检索损失
        retrieval_loss = self.retrieval_task(
            query_embeddings=user_embeddings,
            candidate_embeddings=positive_movie_embeddings
        )

        # 评分损失
        rating_predictions = self.rating_model(
            tf.concat([user_embeddings, positive_movie_embeddings], axis=1)
        )

        rating_loss = self.rating_task(
            labels=features["user_rating"],
            predictions=rating_predictions
        )

        # 组合损失 - 简化权重
        return retrieval_loss + rating_loss

def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")
    
    # 创建模拟的用户-电影评分数据
    users = ["user_1", "user_2", "user_3", "user_4", "user_5"] * 20
    movies = ["movie_1", "movie_2", "movie_3", "movie_4", "movie_5"] * 20
    ratings = np.random.uniform(1, 5, 100)  # 1-5分的评分
    
    # 打乱数据
    indices = np.random.permutation(100)
    users = [users[i] for i in indices]
    movies = [movies[i] for i in indices]
    ratings = ratings[indices]
    
    # 创建数据集
    dataset = tf.data.Dataset.from_tensor_slices({
        "user_id": users,
        "movie_title": movies,
        "user_rating": ratings.astype(np.float32)
    })
    
    return dataset

def train_model():
    """训练推荐模型"""
    print("开始训练推荐模型...")
    
    # 创建数据
    dataset = create_sample_data()
    
    # 数据预处理
    shuffled = dataset.shuffle(100000, seed=42, reshuffle_each_iteration=False)
    train = shuffled.take(80).batch(8).cache()
    test = shuffled.skip(80).take(20).batch(4).cache()
    
    # 创建模型
    model = SimpleRecommenderModel()
    model.compile(optimizer=tf.keras.optimizers.Adagrad(learning_rate=0.1))
    
    # 训练模型
    print("训练中...")
    model.fit(train, epochs=3, verbose=1)
    
    # 评估模型
    print("评估模型...")
    metrics = model.evaluate(test, return_dict=True)
    print("评估指标:")
    for key, value in metrics.items():
        print(f"  {key}: {value:.3f}")

    # 获取RMSE指标
    rmse = metrics.get('root_mean_squared_error', 0.0)
    print(f"\n评分预测 RMSE: {rmse:.3f}")
    
    return model

def make_recommendations(model, user_id="user_1", k=3):
    """为指定用户生成推荐"""
    print(f"\n为用户 {user_id} 生成 top-{k} 推荐:")

    # 获取用户嵌入
    user_input = tf.constant([user_id])
    user_emb = model.user_embedding(user_input)

    # 获取所有电影的嵌入
    movie_input = tf.constant(model._movie_vocab)
    movie_embs = model.movie_embedding(movie_input)

    # 计算相似度分数
    scores = tf.linalg.matmul(user_emb, movie_embs, transpose_b=True)

    # 获取 top-k 推荐
    _, top_indices = tf.nn.top_k(scores[0], k=k)

    print("推荐的电影:")
    for i, idx in enumerate(top_indices.numpy()):
        movie = model._movie_vocab[idx]
        score = scores[0][idx].numpy()
        print(f"{i+1}. {movie} (相似度分数: {score:.3f})")

def main():
    """主函数"""
    print("=" * 50)
    print("TensorFlow Recommenders 简单推荐系统示例")
    print("=" * 50)
    
    # 训练模型
    model = train_model()
    
    # 生成推荐
    make_recommendations(model, "user_1", k=3)
    make_recommendations(model, "user_2", k=3)
    
    print("\n" + "=" * 50)
    print("推荐系统演示完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
