#!/usr/bin/env python3
"""
测试改进的负样本采集策略

验证以下改进：
1. Pointwise训练 (正负样本比例1:2或1:3)
2. In-batch负采样的热门偏差修正
3. 训练时使用 cos(a,bi) - log(pi)，推理时使用 cos(a,bi)
"""

import sys
import os
import numpy as np
import pandas as pd
import tensorflow as tf

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from movielens_two_tower import (
    MovieLensDataLoader, 
    TwoTowerModel,
    compute_item_popularity,
    create_pointwise_dataset,
    create_dataset
)

def test_negative_sampling():
    """测试负样本采集策略"""
    print("🧪 测试改进的负样本采集策略")
    print("="*60)
    
    # 1. 加载数据
    print("1. 加载MovieLens数据...")
    loader = MovieLensDataLoader()
    if not loader.load_data():
        print("❌ 数据加载失败！")
        return
    
    df = loader.preprocess_data()
    vocabs = loader.get_vocabularies(df)
    
    print(f"✅ 数据加载成功")
    print(f"   用户数: {len(vocabs['user_id'])}")
    print(f"   电影数: {len(vocabs['movie_id'])}")
    print(f"   交互数: {len(df)}")
    
    # 2. 测试物品流行度计算
    print("\n2. 测试物品流行度计算...")
    item_popularity, item_log_prob = compute_item_popularity(df)
    
    # 显示最热门和最冷门的电影
    sorted_popularity = sorted(item_popularity.items(), key=lambda x: x[1], reverse=True)
    print(f"✅ 流行度计算完成")
    print(f"   最热门电影: ID={sorted_popularity[0][0]}, 流行度={sorted_popularity[0][1]:.6f}")
    print(f"   最冷门电影: ID={sorted_popularity[-1][0]}, 流行度={sorted_popularity[-1][1]:.6f}")
    print(f"   流行度比例: {sorted_popularity[0][1] / sorted_popularity[-1][1]:.1f}:1")
    
    # 3. 测试pointwise数据集创建
    print("\n3. 测试pointwise数据集创建...")
    
    # 使用小样本测试
    sample_df = df.sample(n=1000, random_state=42)
    sample_vocabs = loader.get_vocabularies(sample_df)
    sample_popularity, sample_log_prob = compute_item_popularity(sample_df)
    
    for neg_ratio in [2, 3]:
        print(f"\n   测试负样本比例 1:{neg_ratio}")
        
        try:
            pointwise_ds = create_pointwise_dataset(
                sample_df, sample_vocabs, sample_popularity, sample_log_prob,
                neg_ratio=neg_ratio, batch_size=64
            )
            
            # 检查数据集结构
            sample_batch = next(iter(pointwise_ds))
            features, labels = sample_batch
            
            print(f"   ✅ 数据集创建成功")
            print(f"      批次大小: {len(labels)}")
            print(f"      特征字段: {list(features.keys())}")
            print(f"      标签分布: 正样本={tf.reduce_sum(features['label']).numpy()}, "
                  f"负样本={len(labels) - tf.reduce_sum(features['label']).numpy()}")
            print(f"      偏差修正范围: {tf.reduce_min(features['bias_correction']).numpy():.4f} ~ "
                  f"{tf.reduce_max(features['bias_correction']).numpy():.4f}")
            
        except Exception as e:
            print(f"   ❌ 数据集创建失败: {e}")
    
    # 4. 测试模型训练
    print("\n4. 测试模型训练...")
    
    try:
        # 创建小规模训练数据
        train_df = sample_df.sample(n=800, random_state=42)
        test_df = sample_df.drop(train_df.index)
        
        train_popularity, train_log_prob = compute_item_popularity(train_df)
        
        # 创建pointwise训练数据集
        train_ds = create_pointwise_dataset(
            train_df, sample_vocabs, train_popularity, train_log_prob,
            neg_ratio=2, batch_size=64
        )
        
        # 创建测试数据集
        test_ds = create_dataset(test_df, batch_size=32)
        
        # 创建模型
        model = TwoTowerModel(sample_vocabs, embedding_dim=32)
        model.compile(optimizer=tf.keras.optimizers.Adam(learning_rate=0.001))
        
        print("   ✅ 模型创建成功")
        
        # 训练几个epoch测试
        print("   🚀 开始训练测试 (2个epoch)...")
        
        history = model.fit(
            train_ds,
            validation_data=test_ds,
            epochs=2,
            verbose=1
        )
        
        print("   ✅ 训练测试完成")
        print(f"      最终训练损失: {history.history['loss'][-1]:.4f}")
        print(f"      最终验证损失: {history.history['val_loss'][-1]:.4f}")
        
        # 5. 测试推理时的相似度计算
        print("\n5. 测试推理时的相似度计算...")
        
        # 选择一个测试用户
        test_user = test_df.iloc[0]
        user_features = {
            'user_id': test_user['user_id'],
            'gender': test_user['gender'],
            'age': test_user['age'],
            'occupation': test_user['occupation']
        }
        
        # 选择几个测试电影
        test_movies = test_df[['movie_id', 'main_genre', 'year']].drop_duplicates().head(5)
        candidate_movies = []
        for _, movie in test_movies.iterrows():
            candidate_movies.append({
                'movie_id': movie['movie_id'],
                'main_genre': movie['main_genre'],
                'year': movie['year']
            })
        
        print(f"   测试用户: {user_features['user_id']}")
        print(f"   候选电影数: {len(candidate_movies)}")
        
        # 测试训练模式和推理模式的差异
        for movie_info in candidate_movies[:2]:  # 只测试前2个
            features = {
                'user_id': tf.constant([user_features['user_id']]),
                'movie_id': tf.constant([movie_info['movie_id']]),
                'gender': tf.constant([user_features['gender']]),
                'age': tf.constant([user_features['age']]),
                'occupation': tf.constant([user_features['occupation']]),
                'main_genre': tf.constant([movie_info['main_genre']]),
                'year': tf.constant([movie_info['year']]),
                'bias_correction': tf.constant([train_log_prob.get(movie_info['movie_id'], 0.0)])
            }
            
            # 训练模式 (应用偏差修正)
            output_train = model(features, training=True)
            cosine_sim = output_train['cosine_similarity'].numpy()[0][0]
            corrected_sim = output_train['corrected_similarity'].numpy()[0][0]
            
            # 推理模式 (不应用偏差修正)
            output_infer = model(features, training=False)
            infer_sim = output_infer['cosine_similarity'].numpy()[0][0]
            
            print(f"   电影 {movie_info['movie_id']}:")
            print(f"      原始相似度: {cosine_sim:.4f}")
            print(f"      训练时修正相似度: {corrected_sim:.4f}")
            print(f"      推理时相似度: {infer_sim:.4f}")
            print(f"      偏差修正量: {cosine_sim - corrected_sim:.4f}")
        
        print("\n✅ 所有测试完成！")
        
        # 6. 总结改进效果
        print("\n📊 改进总结:")
        print("✅ Pointwise训练: 支持1:2和1:3的正负样本比例")
        print("✅ 热门偏差修正: 训练时应用 cos(a,bi) - log(pi)")
        print("✅ 推理时还原: 推理时使用原始 cos(a,bi)")
        print("✅ 负采样策略: 基于流行度的负样本采样")
        print("✅ 数据集兼容: 支持新旧数据集格式")
        
    except Exception as e:
        print(f"   ❌ 模型训练测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_negative_sampling()
